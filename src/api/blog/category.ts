import { fetcher } from '@/api';
import { BLOG_ENDPOINT } from '@/constants/endpoint';

export interface ICategoryResponse {
  data: ICategoryData[];
  error: boolean;
  message: string | null;
}

export interface ICategoryData {
  id: number;
  name: string;
  slug: string;
  icon?: string;
  description: string;
  posts_count?: number;
}

export const blogCategoryApi = {
  getAll: async (): Promise<{data: ICategoryData[] | null; error: any; meta: any}> => {
    return fetcher<ICategoryData[]>({ 
      endpoint: BLOG_ENDPOINT.GET_CATEGORIES, 
      type: 'blog' 
    });
  }
};
