import React from "react";
import Navbar from '@/components/Layout/Navbar'
import MainBanner from '@/components/ITStartup/MainBanner';
import Features from "@/components/ITStartup/Features";
import ServicesArea from "@/components/ITStartup/ServicesArea";
import Team from "@/components/Common/Team";
import FunFactsArea from "@/components/Common/FunFactsArea";
import RecentWorks from "@/components/Common/RecentWorks";
import PricingStyleOne from "@/components/PricingPlans/PricingStyleOne";
import Feedback from "@/components/Common/Feedback";
import Partner from "@/components/Common/Partner";
import BlogPost from "@/components/Common/BlogPost";
import Footer from "@/components/Layout/Footer";
import OurKeyAdvantages from "@/components/ITStartup/OurKeyAdvantages";
import Technologies from "@/components/Common/Technologies";
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('home');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}`;
  const title = t('title', { default: 'Home - CSlant Solutions | Nhà Cung Cấp Giải Pháp Công Nghệ' });
  const description = t('description', { default: 'CSlant Solutions là một công ty công nghệ toàn diện cung cấp các giải pháp tư duy đến cuối để xây dựng, quản lý và mở rộng quy mô doanh nghiệp của bạn. Đội ngũ các chuyên gia' });

  return {
    title: {
      default: title,
      template: '%s | CSlant',
    },
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com',
      languages: {
        en: 'https://cslant.com',
        vi: 'https://cslant.com/vi',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default function Home() {
  return (
    <>
      <Navbar />

      <MainBanner />

      <Features />

      <ServicesArea />

      <OurKeyAdvantages />

      <Team />

      <FunFactsArea />

      {/*<RecentWorks />*/}

      <PricingStyleOne />

      {/*<Feedback />*/}

      {/*<Partner />*/}
      <Technologies />

      <BlogPost />

      <Footer />
    </>
  );
}
