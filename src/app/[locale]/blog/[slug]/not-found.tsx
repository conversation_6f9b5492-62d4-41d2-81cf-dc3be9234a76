import React from 'react';
import Link from 'next/link';
import Navbar from '@/components/Layout/Navbar';
import Footer from '@/components/Layout/Footer';
import PageBanner from '@/components/Common/PageBanner';

export default function NotFound() {
  return (
    <>
      <Navbar />
      
      <PageBanner pageTitle="Blog Post Not Found" />
      
      <div className="error-area ptb-80">
        <div className="container">
          <div className="error-content">
            <div className="row align-items-center">
              <div className="col-lg-6 col-md-12">
                <div className="error-text">
                  <h1>404</h1>
                  <h3>Blog Post Not Found</h3>
                  <p>
                    The blog post you are looking for might have been removed, 
                    had its name changed, or is temporarily unavailable.
                  </p>
                  
                  <div className="error-btn">
                    <Link href="/blog" className="btn btn-primary">
                      Back to Blog
                    </Link>
                  </div>
                </div>
              </div>
              
              <div className="col-lg-6 col-md-12">
                <div className="error-image">
                  <img 
                    src="/images/error.png" 
                    alt="Blog post not found"
                    style={{ maxWidth: '100%', height: 'auto' }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </>
  );
}
