import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import FaqContent from "@/components/FAQ/FaqContent";
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}faq`;
  const title = locale === 'vi' ? 'Câu Hỏi Thường Gặp | CSlant' : 'FAQ | CSlant';
  const description = locale === 'vi'
    ? 'Tìm câu trả lời cho các câu hỏi thường gặp về dịch vụ và giải pháp của <PERSON>.'
    : 'Find answers to frequently asked questions about CSlant services and solutions.';
  // const imageUrl = 'https://cslant.com/images/og-faq.jpg';

  return {
    title: {
      default: title,
      template: '%s | CSlant',
    },
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/faq',
      languages: {
        en: 'https://cslant.com/faq',
        vi: 'https://cslant.com/vi/faq',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

  };
}

export default function Page() {
  const t = useTranslations('pages.faq');

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} />

      <FaqContent />

      <Footer />
    </>
  );
};
