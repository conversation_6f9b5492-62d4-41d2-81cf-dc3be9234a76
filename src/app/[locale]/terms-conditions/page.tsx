import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import TermsConditionsContent from "@/components/TermsConditions/TermsConditionsContent";
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}terms-conditions`;
  const title = locale === 'vi' ? 'Điều Khoản & Điề<PERSON> | CSlant' : 'Terms & Conditions | CSlant';
  const description = locale === 'vi'
    ? 'Đ<PERSON><PERSON> c<PERSON>c điều khoản và điều kiện của <PERSON> khi sử dụng dịch vụ và website của chúng tôi.'
    : 'Read CSlant\'s terms and conditions for using our services and website.';
  // const imageUrl = 'https://cslant.com/images/og-terms-conditions.jpg';

  return {
    title: {
      default: title,
      template: '%s | CSlant',
    },
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/terms-conditions',
      languages: {
        en: 'https://cslant.com/terms-conditions',
        vi: 'https://cslant.com/vi/terms-conditions',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

  };
}

export default function Page() {
  const t = useTranslations('pages.terms-conditions');
  
  return (
    <>
      <Navbar />

      <PageBanner 
        pageTitle={t('title')} 
        headingNumber={1}
      />

      <TermsConditionsContent />

      <Footer />
    </>
  );
};
